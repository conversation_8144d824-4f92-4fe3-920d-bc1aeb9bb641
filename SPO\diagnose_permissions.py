#!/usr/bin/env python3
"""
SharePoint Permission Diagnostic Script
This script helps diagnose permission issues with SharePoint app-only authentication
"""

import sys
import requests
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.client_credential import ClientCredential

# Same credentials as main script
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
CLIENT_SECRET = "****************************************"

def test_token_acquisition():
    """Test if we can get an access token"""
    print("=== Testing Token Acquisition ===")
    
    # Token endpoint for app-only authentication
    token_url = f"https://accounts.accesscontrol.windows.net/{TENANT_ID}/tokens/OAuth/2"
    
    # Prepare the request
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    # SharePoint resource
    resource = f"********-0000-0ff1-ce00-************/jabil.sharepoint.com@{TENANT_ID}"
    
    data = {
        'grant_type': 'client_credentials',
        'client_id': f"{CLIENT_ID}@{TENANT_ID}",
        'client_secret': CLIENT_SECRET,
        'resource': resource
    }
    
    try:
        response = requests.post(token_url, headers=headers, data=data, timeout=30)
        print(f"Token request status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            print("✓ Successfully obtained access token")
            print(f"Token type: {token_data.get('token_type', 'Unknown')}")
            # Don't print the actual token for security
            print(f"Token expires in: {token_data.get('expires_in', 'Unknown')} seconds")
            return True
        else:
            print(f"✗ Token acquisition failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Token acquisition error: {str(e)}")
        return False

def test_different_endpoints():
    """Test different SharePoint endpoints to see which ones work"""
    print("\n=== Testing Different Endpoints ===")
    
    creds = ClientCredential(CLIENT_ID, CLIENT_SECRET)
    
    # Test different endpoints
    endpoints_to_test = [
        ("Site Web Info", "/_api/Web"),
        ("Site Lists", "/_api/Web/Lists"),
        ("Current User", "/_api/Web/CurrentUser"),
        ("Site Users", "/_api/Web/SiteUsers"),
        ("Site Groups", "/_api/Web/SiteGroups"),
    ]
    
    for name, endpoint in endpoints_to_test:
        try:
            print(f"\nTesting {name} ({endpoint})...")
            ctx = ClientContext(SITE).with_credentials(creds)
            
            # Make a direct REST API call
            url = SITE + endpoint
            response = ctx.execute_request_direct(url)
            
            if response.status_code == 200:
                print(f"✓ {name}: SUCCESS")
            else:
                print(f"✗ {name}: FAILED ({response.status_code})")
                
        except Exception as e:
            print(f"✗ {name}: ERROR - {str(e)}")

def check_app_permissions():
    """Provide guidance on checking app permissions"""
    print("\n=== App Permission Checklist ===")
    print("To resolve the 403 Forbidden error, verify the following:")
    print()
    print("1. Azure AD App Registration:")
    print("   - Go to https://portal.azure.com")
    print("   - Navigate to Azure Active Directory > App registrations")
    print(f"   - Find your app with Client ID: {CLIENT_ID}")
    print()
    print("2. API Permissions:")
    print("   - Check 'API permissions' tab")
    print("   - Should have 'SharePoint' permissions:")
    print("     * Sites.Selected (Application)")
    print("     * OR Sites.ReadWrite.All (Application)")
    print("   - Ensure admin consent is granted (green checkmarks)")
    print()
    print("3. SharePoint Site Permissions:")
    print("   - The app needs to be granted access to the specific site")
    print("   - This is done via PowerShell or SharePoint Admin Center")
    print("   - For Sites.Selected permission, run:")
    print(f"     Grant-PnPAzureADAppSitePermission -AppId '{CLIENT_ID}' -DisplayName 'YourAppName' -Site '{SITE}' -Permissions Write")
    print()
    print("4. Alternative: Use Sites.ReadWrite.All")
    print("   - This gives access to all sites (broader permission)")
    print("   - No need for site-specific grants")
    print("   - Requires tenant admin consent")

if __name__ == "__main__":
    print("SharePoint Permission Diagnostic Tool")
    print("=====================================")
    
    # Test token acquisition
    token_success = test_token_acquisition()
    
    if token_success:
        print("\n✓ Token acquisition successful - credentials are valid")
        # Test different endpoints
        test_different_endpoints()
    else:
        print("\n✗ Token acquisition failed - check credentials")
    
    # Always show permission guidance
    check_app_permissions()
    
    print("\n=== Diagnostic Complete ===")
