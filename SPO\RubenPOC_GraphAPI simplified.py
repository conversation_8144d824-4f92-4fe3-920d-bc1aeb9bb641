import sys, requests, time
from datetime import datetime

# App credentials
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
CLIENT_SECRET = "****************************************"

class GraphClient:
    def __init__(self, client_id, client_secret, tenant_id):
        self.client_id, self.client_secret, self.tenant_id = client_id, client_secret, tenant_id
        self.token = None
        self.site_id = None

    def auth(self):
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        data = {
            'grant_type':'client_credentials',
            'client_id':self.client_id,
            'client_secret':self.client_secret,
            'scope':'https://graph.microsoft.com/.default'
        }
        r = requests.post(url, data=data)
        if r.ok: self.token = r.json()['access_token']; return True
        print("Token request failed:", r.text); return False

    def headers(self):
        return {'Authorization': f'Bearer {self.token}', 'Content-Type': 'application/json'}

    def get_site_id(self, site_url):
        path = site_url.replace("https://", "").replace("/sites/", ":/sites/")
        r = requests.get(f"https://graph.microsoft.com/v1.0/sites/{path}", headers=self.headers())
        if r.ok: self.site_id = r.json()['id']; return self.site_id
        print("Failed to get site info:", r.text); return None

    def get_drives(self):
        r = requests.get(f"https://graph.microsoft.com/v1.0/sites/{self.site_id}/drives", headers=self.headers())
        return r.json().get('value', []) if r.ok else []

    def get_items(self, drive_id, folder=""):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root{':' + folder + ':' if folder else ''}/children"
        r = requests.get(url, headers=self.headers())
        return r.json().get('value', []) if r.ok else []

    def create_folder(self, drive_id, name):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
        data = {"name": name, "folder": {}, "@microsoft.graph.conflictBehavior": "replace"}
        r = requests.post(url, headers=self.headers(), json=data)
        return r.ok

    def delete_file(self, drive_id, path):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{path}"
        return requests.delete(url, headers=self.headers()).status_code == 204

    def upload_file(self, drive_id, path, content):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{path}:/content"
        r = requests.put(url, headers={**self.headers(), 'Content-Type':'text/plain'}, data=content)
        return r.json() if r.ok else None

def preview_items(items, indent="  "):
    for i in items:
        typ = '📁 Folder' if 'folder' in i else '📄 File'
        name = i.get('name', 'Unknown')
        size = i.get('size', 0)
        size_str = f" ({format_size(size)})" if size > 0 else ""
        print(f"{indent}- {typ}: {name}{size_str}")

def format_size(bytes):
    if bytes == 0: return "0 B"
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes < 1024: return f"{bytes:.1f} {unit}"
        bytes /= 1024
    return f"{bytes:.1f} TB"

def show_folder_contents(client, drive_id, expanded_folders=None):
    """Show folder contents with optional expansion of specific folders"""
    # Use direct Graph API call instead of client.get_items to avoid caching
    url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
    headers = {
        'Authorization': f'Bearer {client.token}',
        'Accept': 'application/json'
    }

    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print("  (No items found)")
        return

    items_data = response.json()
    items = items_data.get('value', [])

    if not items:
        print("  (No items found)")
        return

    expanded_folders = expanded_folders or []

    for item in items:
        is_folder = 'folder' in item
        name = item.get('name', 'Unknown')
        size = item.get('size', 0)

        if is_folder:
            print(f"  📁 {name}/")
            # Expand specific folders
            if name in expanded_folders:
                # Direct API call for folder contents to avoid caching
                folder_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{name}:/children"
                folder_response = requests.get(folder_url, headers=headers)

                if folder_response.status_code == 200:
                    folder_data = folder_response.json()
                    sub_items = folder_data.get('value', [])

                    if sub_items:
                        print(f"    └── Contents ({len(sub_items)} items):")
                        for sub_item in sub_items:
                            sub_is_folder = 'folder' in sub_item
                            sub_name = sub_item.get('name', 'Unknown')
                            sub_size = sub_item.get('size', 0)
                            sub_icon = '📁' if sub_is_folder else '📄'
                            sub_size_str = f" ({format_size(sub_size)})" if sub_size > 0 else ""
                            print(f"        {sub_icon} {sub_name}{sub_size_str}")
                    else:
                        print(f"    └── (Empty folder)")
                else:
                    print(f"    └── (Unable to access folder contents)")
        else:
            size_str = f" ({format_size(size)})" if size > 0 else ""
            print(f"  📄 {name}{size_str}")

def main():
    print("\n=== SharePoint POC using Microsoft Graph API ===\n")
    client = GraphClient(CLIENT_ID, CLIENT_SECRET, TENANT_ID)
    if not client.auth(): sys.exit(1)
    if not client.get_site_id(SITE): sys.exit(1)

    drives = client.get_drives()
    main_drive = next((d for d in drives if d['name']=="Documents"), drives[0])
    print(f"Connected to site: {SITE}")
    print(f"Using drive: {main_drive['name']} (ID: {main_drive['id']})\n")

    print("1) Available drives:")
    for d in drives:
        print(f"   - {d['name']} (ID: {d['id']})")
    print("")

    # Show initial directory structure with RubenTest expanded
    print(f"2) Initial directory structure in drive: {main_drive['name']}")
    print("=" * 60)
    show_folder_contents(client, main_drive['id'], expanded_folders=["RubenTest"])
    print("=" * 60)

    # Manage RubenTest folder
    print(f"\n3) Managing RubenTest folder...")
    folder_name = "RubenTest"

    # Check if RubenTest folder exists
    items = client.get_items(main_drive['id'])
    ruben_folder_exists = False

    if items:
        for item in items:
            if item.get('name') == folder_name and 'folder' in item:
                ruben_folder_exists = True
                break

    if ruben_folder_exists:
        print("   ℹ️  RubenTest folder already exists")

        # Check for existing files in RubenTest folder using direct API call
        print("   📋 Checking for existing files...")
        folder_url = f"https://graph.microsoft.com/v1.0/drives/{main_drive['id']}/root:/{folder_name}:/children"
        headers = {
            'Authorization': f'Bearer {client.token}',
            'Accept': 'application/json'
        }

        folder_response = requests.get(folder_url, headers=headers)

        if folder_response.status_code == 200:
            folder_data = folder_response.json()
            ruben_items = folder_data.get('value', [])

            if ruben_items:
                files_to_delete = [item for item in ruben_items if 'file' in item]
                if files_to_delete:
                    print(f"   📄 Found {len(files_to_delete)} files to delete:")
                    for file_item in files_to_delete:
                        file_name_del = file_item.get('name', 'Unknown')
                        file_size = file_item.get('size', 0)
                        print(f"      - {file_name_del} ({format_size(file_size)})")

                    # Delete existing files
                    print("   🗑️  Deleting existing files...")
                    for file_item in files_to_delete:
                        file_name_del = file_item.get('name', 'Unknown')
                        file_path = f"{folder_name}/{file_name_del}"
                        if client.delete_file(main_drive['id'], file_path):
                            print(f"      ✓ Deleted: {file_name_del}")
                        else:
                            print(f"      ✗ Failed to delete: {file_name_del}")
                else:
                    print("   📂 No files found in RubenTest folder")
            else:
                print("   📂 RubenTest folder is empty")
        else:
            print("   📂 RubenTest folder is empty")
    else:
        print("   📁 Creating new RubenTest folder...")
        if client.create_folder(main_drive['id'], folder_name):
            print("      ✓ RubenTest folder created successfully")
        else:
            print("      ✗ Failed to create RubenTest folder")
            sys.exit(1)

    # Create new timestamped file
    print(f"\n4) Creating new test file...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"test_file_{timestamp}.txt"
    content = f"Test file created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\nThis file was created by the RubenPOC simplified script.\nTimestamp: {timestamp}"

    uploaded = client.upload_file(main_drive['id'], f"{folder_name}/{file_name}", content)
    if uploaded:
        print(f"   ✓ Created file: {file_name}")
        print(f"   📊 File size: {format_size(uploaded.get('size', 0))}")
        print(f"   🔗 Web URL: {uploaded.get('webUrl', 'Not available')}")
    else:
        print(f"   ✗ Failed to create file: {file_name}")

    # Wait a moment for changes to propagate
    print("   ⏳ Waiting for changes to propagate...")
    time.sleep(2)

    # Show final directory structure with RubenTest expanded
    print(f"\n5) Final directory structure in drive: {main_drive['name']}")
    print("=" * 60)
    show_folder_contents(client, main_drive['id'], expanded_folders=["RubenTest"])
    print("=" * 60)

    print("\n🎉 Script completed successfully!")

if __name__ == "__main__":
    main()
