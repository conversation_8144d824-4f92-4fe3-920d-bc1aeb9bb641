import sys, requests
from datetime import datetime

# App credentials
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
CLIENT_SECRET = "****************************************"

class GraphClient:
    def __init__(self, client_id, client_secret, tenant_id):
        self.client_id, self.client_secret, self.tenant_id = client_id, client_secret, tenant_id
        self.token = None
        self.site_id = None

    def auth(self):
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        data = {
            'grant_type':'client_credentials',
            'client_id':self.client_id,
            'client_secret':self.client_secret,
            'scope':'https://graph.microsoft.com/.default'
        }
        r = requests.post(url, data=data)
        if r.ok: self.token = r.json()['access_token']; return True
        print("Token request failed:", r.text); return False

    def headers(self):
        return {'Authorization': f'Bearer {self.token}', 'Content-Type': 'application/json'}

    def get_site_id(self, site_url):
        path = site_url.replace("https://", "").replace("/sites/", ":/sites/")
        r = requests.get(f"https://graph.microsoft.com/v1.0/sites/{path}", headers=self.headers())
        if r.ok: self.site_id = r.json()['id']; return self.site_id
        print("Failed to get site info:", r.text); return None

    def get_drives(self):
        r = requests.get(f"https://graph.microsoft.com/v1.0/sites/{self.site_id}/drives", headers=self.headers())
        return r.json().get('value', []) if r.ok else []

    def get_items(self, drive_id, folder=""):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root{':' + folder + ':' if folder else ''}/children"
        r = requests.get(url, headers=self.headers())
        return r.json().get('value', []) if r.ok else []

    def create_folder(self, drive_id, name):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
        data = {"name": name, "folder": {}, "@microsoft.graph.conflictBehavior": "replace"}
        r = requests.post(url, headers=self.headers(), json=data)
        return r.ok

    def delete_file(self, drive_id, path):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{path}"
        return requests.delete(url, headers=self.headers()).status_code == 204

    def upload_file(self, drive_id, path, content):
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{path}:/content"
        r = requests.put(url, headers={**self.headers(), 'Content-Type':'text/plain'}, data=content)
        return r.json() if r.ok else None

def preview_items(items):
    for i in items:
        typ = '📁 Folder' if 'folder' in i else '📄 File'
        name = i.get('name', 'Unknown')
        size = i.get('size', 0)
        print(f"  - {typ}: {name}" + (f" ({size} bytes)" if size else ""))

def main():
    print("\n=== SharePoint POC using Microsoft Graph API ===\n")
    client = GraphClient(CLIENT_ID, CLIENT_SECRET, TENANT_ID)
    if not client.auth(): sys.exit(1)
    if not client.get_site_id(SITE): sys.exit(1)

    drives = client.get_drives()
    main_drive = next((d for d in drives if d['name']=="Documents"), drives[0])
    print(f"Connected to site: {SITE}")
    print(f"Using drive: {main_drive['name']} (ID: {main_drive['id']})\n")

    print("Drives preview:")
    for d in drives:
        print(f"- {d['name']} (ID: {d['id']})")
        items = client.get_items(d['id'])
        preview_items(items)
    print("")

    folder_name = "RubenTest"
    items = client.get_items(main_drive['id'], folder_name)
    if items:
        print(f"Cleaning existing files in {folder_name}...")
        for f in [i for i in items if 'file' in i]:
            path = f"{folder_name}/{f['name']}"
            if client.delete_file(main_drive['id'], path):
                print(f"  - Deleted file: {f['name']}")
            else:
                print(f"  - Failed to delete: {f['name']}")
    else:
        print(f"Creating folder {folder_name}...")
        if client.create_folder(main_drive['id'], folder_name):
            print(f"  - Folder created: {folder_name}")
        else:
            print(f"  - Failed to create folder: {folder_name}")
            sys.exit(1)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"test_file_{timestamp}.txt"
    content = f"POC test file created at {datetime.now()}\nTimestamp: {timestamp}"
    uploaded = client.upload_file(main_drive['id'], f"{folder_name}/{file_name}", content)
    if uploaded:
        print(f"\nFile uploaded: {uploaded.get('webUrl')}")
        print(f"File size: {uploaded.get('size', 'Unknown')} bytes\n")
    else:
        print("Failed to upload file\n")

    print("=== POC Completed Successfully! ===\n")

if __name__ == "__main__":
    main()
