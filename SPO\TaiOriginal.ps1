# Connect-MgGraph

$site = Get-MgSite -SiteId "jabil.sharepoint.com:/teams/IT/STT/GSOGeneral:"
$drive = (Get-MgSiteDrive -SiteId $site.Id)[0]

function Get-Folder($id = "root", $path = "Root") {
    $folders = Get-MgDriveItemChild -DriveId $drive.Id -DriveItemId $id | ? Folder
    if (!$folders) { return $id }
    
    $options = @("Use this folder") + $folders.Name
    $sel = 0
    
    do {
        cls; Write-Host "$path - Use ↑↓ Enter" -f Cyan
        0..($options.Count-1) | % { Write-Host $(if($_ -eq $sel){"→ $($options[$_])"}else{"  $($options[$_])"}) -f $(if($_ -eq $sel){"Black"}else{"White"}) -b $(if($_ -eq $sel){"Green"}else{"Black"}) }
        $k = $Host.UI.RawUI.ReadKey("NoE<PERSON>,IncludeKeyDown").VirtualKeyCode
        if($k -eq 38 -and $sel -gt 0) { $sel-- }
        if($k -eq 40 -and $sel -lt $options.Count-1) { $sel++ }
    } while ($k -ne 13)
    
    if ($sel -eq 0) { $id } else { Get-Folder $folders[$sel-1].Id "$path/$($folders[$sel-1].Name)" }
}

$files = Get-MgDriveItemChild -DriveId $drive.Id -DriveItemId (Get-Folder) | ? File
Write-Host "Found $($files.Count) files. Generate links? (Y/n):" -f Green -NoNewline
if ((Read-Host) -match "^(|y|Y)$") {
    $files | % { 
        # Not working
        # $link = New-MgDriveItemCreateLink -DriveId $drive.Id -DriveItemId $_.Id -Type view -Scope anonymous

        # Working
        $link = New-MgDriveItemLink -DriveId $drive.Id -DriveItemId $file.Id -Type "view" -Scope "organization"

        "$($_.Name)`n$($link.Link.WebUrl)`n"
    }
}