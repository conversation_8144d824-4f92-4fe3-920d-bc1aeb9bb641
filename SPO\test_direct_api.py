#!/usr/bin/env python3
"""
Direct SharePoint REST API test using raw HTTP requests
This bypasses the Office365-REST-Python-Client library to test raw API access
"""

import requests
import json

# App credentials
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
CLIENT_SECRET = "****************************************"

def get_access_token():
    """Get access token using client credentials flow"""
    print("Getting access token...")
    
    # Microsoft Graph token endpoint
    token_url = f"https://login.microsoftonline.com/{TENANT_ID}/oauth2/v2.0/token"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    # Use SharePoint scope
    data = {
        'grant_type': 'client_credentials',
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET,
        'scope': 'https://graph.microsoft.com/.default'
    }
    
    try:
        response = requests.post(token_url, headers=headers, data=data)
        if response.status_code == 200:
            token_data = response.json()
            print("✓ Got Microsoft Graph token")
            return token_data['access_token']
        else:
            print(f"✗ Token request failed: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"✗ Token request error: {e}")
        return None

def get_sharepoint_token():
    """Get SharePoint-specific access token"""
    print("Getting SharePoint-specific token...")
    
    # SharePoint token endpoint
    token_url = f"https://accounts.accesscontrol.windows.net/{TENANT_ID}/tokens/OAuth/2"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    # SharePoint resource
    resource = f"********-0000-0ff1-ce00-************/jabil.sharepoint.com@{TENANT_ID}"
    
    data = {
        'grant_type': 'client_credentials',
        'client_id': f"{CLIENT_ID}@{TENANT_ID}",
        'client_secret': CLIENT_SECRET,
        'resource': resource
    }
    
    try:
        response = requests.post(token_url, headers=headers, data=data)
        if response.status_code == 200:
            token_data = response.json()
            print("✓ Got SharePoint token")
            return token_data['access_token']
        else:
            print(f"✗ SharePoint token request failed: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"✗ SharePoint token request error: {e}")
        return None

def test_graph_api(token):
    """Test Microsoft Graph API access to SharePoint"""
    print("\n=== Testing Microsoft Graph API ===")
    
    # Extract site info from URL for Graph API
    # https://jabil.sharepoint.com/sites/LuminexData -> jabil.sharepoint.com:/sites/LuminexData
    site_path = "jabil.sharepoint.com:/sites/LuminexData"
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json'
    }
    
    # Test site access via Graph API
    graph_url = f"https://graph.microsoft.com/v1.0/sites/{site_path}"
    
    try:
        response = requests.get(graph_url, headers=headers)
        print(f"Graph API site request: {response.status_code}")
        
        if response.status_code == 200:
            site_data = response.json()
            print("✓ Graph API site access successful!")
            print(f"  Site Name: {site_data.get('displayName', 'Unknown')}")
            print(f"  Site ID: {site_data.get('id', 'Unknown')}")
            
            # Test document libraries
            site_id = site_data.get('id')
            if site_id:
                drives_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives"
                drives_response = requests.get(drives_url, headers=headers)
                
                if drives_response.status_code == 200:
                    drives_data = drives_response.json()
                    print(f"✓ Found {len(drives_data.get('value', []))} document libraries")
                    for drive in drives_data.get('value', []):
                        print(f"  - {drive.get('name', 'Unknown')}")
                else:
                    print(f"✗ Drives request failed: {drives_response.status_code}")
            
            return True
        else:
            print(f"✗ Graph API failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"✗ Graph API error: {e}")
        return False

def test_sharepoint_rest_api(token):
    """Test direct SharePoint REST API"""
    print("\n=== Testing SharePoint REST API ===")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json;odata=verbose'
    }
    
    # Test basic web info
    api_url = f"{SITE}/_api/web"
    
    try:
        response = requests.get(api_url, headers=headers)
        print(f"SharePoint REST API request: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ SharePoint REST API access successful!")
            web_data = response.json()
            d = web_data.get('d', {})
            print(f"  Site Title: {d.get('Title', 'Unknown')}")
            print(f"  Site URL: {d.get('Url', 'Unknown')}")
            return True
        else:
            print(f"✗ SharePoint REST API failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"✗ SharePoint REST API error: {e}")
        return False

if __name__ == "__main__":
    print("=== Direct API Access Test ===")
    print(f"Site: {SITE}")
    print(f"Client ID: {CLIENT_ID}")
    print()
    
    # Test Microsoft Graph approach
    graph_token = get_access_token()
    if graph_token:
        graph_success = test_graph_api(graph_token)
    else:
        graph_success = False
    
    # Test SharePoint-specific token approach
    sp_token = get_sharepoint_token()
    if sp_token:
        sp_success = test_sharepoint_rest_api(sp_token)
    else:
        sp_success = False
    
    print("\n=== Results Summary ===")
    print(f"Microsoft Graph API: {'✓ SUCCESS' if graph_success else '✗ FAILED'}")
    print(f"SharePoint REST API: {'✓ SUCCESS' if sp_success else '✗ FAILED'}")
    
    if graph_success or sp_success:
        print("\n🎉 At least one API method works!")
        print("The issue might be with the Office365-REST-Python-Client library configuration.")
    else:
        print("\n❌ Both API methods failed.")
        print("This suggests a fundamental permission or configuration issue.")
