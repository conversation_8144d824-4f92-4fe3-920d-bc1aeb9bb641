#!/usr/bin/env python3
"""
Simple SharePoint connection test
Run this after granting site permissions to verify access
"""

import sys
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.client_credential import ClientCredential

# Same credentials as main script
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
CLIENT_SECRET = "****************************************"

def test_basic_access():
    """Test basic SharePoint site access"""
    print("=== SharePoint Connection Test ===")
    print(f"Site: {SITE}")
    print(f"Client ID: {CLIENT_ID}")
    print()
    
    try:
        # Create context and authenticate
        creds = ClientCredential(CLIENT_ID, CLIENT_SECRET)
        ctx = ClientContext(SITE).with_credentials(creds)
        
        # Try to get basic site information
        print("Testing basic site access...")
        web = ctx.web.get().execute_query()
        
        print("✓ SUCCESS! Site access granted")
        print(f"  Site Title: {web.properties.get('Title', 'Unknown')}")
        print(f"  Site URL: {web.properties.get('Url', 'Unknown')}")
        print(f"  Site Description: {web.properties.get('Description', 'No description')}")
        
        # Test listing document libraries
        print("\nTesting document library access...")
        lists = ctx.web.lists.get().execute_query()
        doc_libraries = [lst for lst in lists if lst.properties.get("BaseTemplate") == 101]
        
        print(f"✓ Found {len(doc_libraries)} document libraries:")
        for lib in doc_libraries:
            print(f"  - {lib.properties.get('Title')}")
        
        return True
        
    except Exception as e:
        print(f"✗ FAILED: {str(e)}")
        
        if "403" in str(e):
            print("\nThis is still a permission issue. Please:")
            print("1. Run the PowerShell script: grant_site_permissions.ps1")
            print("2. Wait a few minutes for permissions to propagate")
            print("3. Verify the app has Sites.Selected permission in Azure AD")
        elif "401" in str(e):
            print("\nThis is an authentication issue. Please check:")
            print("1. Client ID and Secret are correct")
            print("2. App registration exists in Azure AD")
        else:
            print(f"\nUnexpected error type: {type(e).__name__}")
            
        return False

if __name__ == "__main__":
    success = test_basic_access()
    
    if success:
        print("\n🎉 Connection test passed! You can now run the main script.")
        print("   Run: python RubenPOC.py")
    else:
        print("\n❌ Connection test failed. Please resolve the issues above.")
        
    sys.exit(0 if success else 1)
