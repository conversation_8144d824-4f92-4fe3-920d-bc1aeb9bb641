#!/usr/bin/env python3
"""
SharePoint Site Access Permission Test
This script tests whether the Client ID has access to different SharePoint sites
to verify that permissions are properly restricted.
"""

import sys
import requests
from datetime import datetime

# App credentials
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
CLIENT_SECRET = "****************************************"

# Sites to test
SITES_TO_TEST = [
    {
        "name": "LuminexData",
        "url": "https://jabil.sharepoint.com/sites/LuminexData",
        "expected_access": True,
        "description": "Should have WRITE access (explicitly granted)"
    },
    {
        "name": "GlobalInformationSecurity-IdentityServices", 
        "url": "https://jabil.sharepoint.com/sites/GlobalInformationSecurity-IdentityServices",
        "expected_access": False,
        "description": "Should be DENIED access (not granted)"
    },
    {
        "name": "HRIT_Integration_SAP_Functional",
        "url": "https://jabil.sharepoint.com/sites/HRIT_Integration_SAP_Functional", 
        "expected_access": False,
        "description": "Should be DENIED access (not granted)"
    }
]

class SiteAccessTester:
    def __init__(self, client_id, client_secret, tenant_id):
        self.client_id = client_id
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        self.access_token = None
    
    def get_access_token(self):
        """Get access token for Microsoft Graph"""
        print("Getting access token...")
        
        token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        
        try:
            response = requests.post(token_url, headers=headers, data=data)
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data['access_token']
                print("✓ Access token obtained successfully")
                return True
            else:
                print(f"✗ Token request failed: {response.status_code}")
                print(response.text)
                return False
        except Exception as e:
            print(f"✗ Token request error: {e}")
            return False
    
    def get_headers(self):
        """Get headers with authorization"""
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json'
        }
    
    def test_site_access(self, site_url, site_name):
        """Test access to a specific SharePoint site"""
        print(f"\n--- Testing: {site_name} ---")
        print(f"URL: {site_url}")
        
        # Convert SharePoint URL to Graph API format
        site_path = site_url.replace('https://', '').replace('/sites/', ':/sites/')
        graph_url = f"https://graph.microsoft.com/v1.0/sites/{site_path}"
        
        try:
            response = requests.get(graph_url, headers=self.get_headers(), timeout=10)
            
            if response.status_code == 200:
                site_data = response.json()
                print(f"✓ ACCESS GRANTED")
                print(f"  Site Name: {site_data.get('displayName', 'Unknown')}")
                print(f"  Site ID: {site_data.get('id', 'Unknown')}")
                
                # Try to get document libraries to test deeper access
                site_id = site_data.get('id')
                if site_id:
                    drives_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives"
                    drives_response = requests.get(drives_url, headers=self.get_headers(), timeout=10)
                    
                    if drives_response.status_code == 200:
                        drives_data = drives_response.json()
                        drive_count = len(drives_data.get('value', []))
                        print(f"  Document Libraries: {drive_count} accessible")
                        
                        # List first few libraries
                        for i, drive in enumerate(drives_data.get('value', [])[:3]):
                            print(f"    - {drive.get('name', 'Unknown')}")
                    else:
                        print(f"  Document Libraries: Access denied ({drives_response.status_code})")
                
                return True
                
            elif response.status_code == 403:
                print(f"✗ ACCESS DENIED (403 Forbidden)")
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'No details')
                print(f"  Reason: {error_msg}")
                return False
                
            elif response.status_code == 404:
                print(f"✗ SITE NOT FOUND (404)")
                print(f"  The site may not exist or is not accessible")
                return False
                
            else:
                print(f"✗ UNEXPECTED RESPONSE ({response.status_code})")
                print(f"  Response: {response.text[:200]}...")
                return False
                
        except requests.exceptions.Timeout:
            print(f"✗ REQUEST TIMEOUT")
            print(f"  The site may not be accessible or network issues")
            return False
            
        except Exception as e:
            print(f"✗ ERROR: {str(e)}")
            return False
    
    def run_access_tests(self, sites):
        """Run access tests for all sites"""
        print("=" * 60)
        print("SHAREPOINT SITE ACCESS PERMISSION TEST")
        print("=" * 60)
        print(f"Client ID: {self.client_id}")
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        results = []
        
        for site in sites:
            site_name = site['name']
            site_url = site['url']
            expected_access = site['expected_access']
            description = site['description']
            
            print(f"\n{'='*20}")
            print(f"TESTING: {site_name}")
            print(f"Expected: {description}")
            print(f"{'='*20}")
            
            actual_access = self.test_site_access(site_url, site_name)
            
            # Determine test result
            if actual_access == expected_access:
                result = "✅ PASS"
                status = "CORRECT"
            else:
                result = "❌ FAIL" 
                status = "INCORRECT"
            
            results.append({
                'site': site_name,
                'expected': expected_access,
                'actual': actual_access,
                'result': result,
                'status': status
            })
            
            print(f"\nResult: {result}")
            if actual_access == expected_access:
                print(f"✓ Permission behavior is correct")
            else:
                print(f"✗ Permission behavior is INCORRECT!")
                if expected_access and not actual_access:
                    print("  Expected access but was denied - missing permissions")
                elif not expected_access and actual_access:
                    print("  Expected denial but got access - overprivileged!")
        
        return results
    
    def print_summary(self, results):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for r in results if r['status'] == 'CORRECT')
        total = len(results)
        
        print(f"Tests Passed: {passed}/{total}")
        print()
        
        for result in results:
            expected_str = "ALLOW" if result['expected'] else "DENY"
            actual_str = "ALLOWED" if result['actual'] else "DENIED"
            print(f"{result['result']} {result['site']}")
            print(f"    Expected: {expected_str} | Actual: {actual_str}")
        
        print("\n" + "=" * 60)
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - Permissions are correctly configured!")
            print("✓ Client ID has access only to authorized sites")
            print("✓ Client ID is properly denied access to unauthorized sites")
        else:
            print("⚠️  SOME TESTS FAILED - Review permission configuration!")
            failed_sites = [r['site'] for r in results if r['status'] == 'INCORRECT']
            print(f"Failed sites: {', '.join(failed_sites)}")
        
        print("=" * 60)

def main():
    tester = SiteAccessTester(CLIENT_ID, CLIENT_SECRET, TENANT_ID)
    
    # Get access token
    if not tester.get_access_token():
        print("Failed to get access token. Exiting.")
        sys.exit(1)
    
    # Run tests
    results = tester.run_access_tests(SITES_TO_TEST)
    
    # Print summary
    tester.print_summary(results)
    
    # Exit with appropriate code
    all_passed = all(r['status'] == 'CORRECT' for r in results)
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    main()
