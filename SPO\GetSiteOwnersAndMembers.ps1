# PnP + Graph API Hybrid Approach
# This uses PnP PowerShell for authentication but Graph API for data retrieval

$AdminAppID = "b2b17e37-dc05-4e41-8559-c9c283af0b8f"
$URL = "https://jabil.sharepoint.com"
$SiteURL = "https://jabil.sharepoint.com/sites/ExtJblMSIOptics"
$TenantId = "jabil.onmicrosoft.com"

# Connect using PnP (your existing method)
Write-Host "Connecting to SharePoint using PnP..." -ForegroundColor Green
$adminconnection = Connect-PnPOnline -url $URL -ClientId $AdminAppID -Tenant $TenantId -CertificatePath 'C:\Users\<USER>\Sharepoint_FullControl_Master.pfx' -ReturnConnection

# Function to make Graph API calls
function Invoke-GraphRequest {
    param(
        [string]$Uri,
        [string]$AccessToken,
        [string]$Method = "GET"
    )
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Content-Type' = 'application/json'
    }
    
    try {
        $response = Invoke-RestMethod -Uri $Uri -Headers $headers -Method $Method
        return $response
    }
    catch {
        Write-Error "Graph API request failed: $($_.Exception.Message)"
        return $null
    }
}

try {
    # Get Graph API access token using PnP
    Write-Host "Getting Graph API access token via PnP..." -ForegroundColor Yellow
    $graphToken = Get-PnPGraphAccessToken -Connection $adminconnection
    
    if (-not $graphToken) {
        Write-Error "Could not obtain Graph API access token"
        exit 1
    }
    
    Write-Host "Graph API token obtained successfully" -ForegroundColor Green
    
    # Now use Graph API to get site information
    Write-Host "`nGetting Site Owners for: $SiteURL" -ForegroundColor Green
    
    # Step 1: Get Site ID using Graph API
    Write-Host "`n--- Step 1: Getting Site Information via Graph API ---" -ForegroundColor Yellow
    
    $hostName = ([System.Uri]$SiteURL).Host
    $sitePath = ([System.Uri]$SiteURL).AbsolutePath
    $graphSiteUrl = "https://graph.microsoft.com/v1.0/sites/$hostName`:$sitePath"
    
    $site = Invoke-GraphRequest -Uri $graphSiteUrl -AccessToken $graphToken
    
    if (-not $site) {
        Write-Error "Could not retrieve site information from Graph API"
        exit 1
    }

    Write-Host "Site ID: $($site.id)" -ForegroundColor Cyan
    Write-Host "Site Name: $($site.displayName)" -ForegroundColor Cyan
    Write-Host "Site URL: $($site.webUrl)" -ForegroundColor Cyan
    
    $siteId = $site.id
    $allOwners = @()
    
    # Step 2: Get Site Lists (including User Information List)
    Write-Host "`n--- Step 2: Getting Site Lists ---" -ForegroundColor Yellow
    
    $listsUrl = "https://graph.microsoft.com/v1.0/sites/$siteId/lists"
    $lists = Invoke-GraphRequest -Uri $listsUrl -AccessToken $graphToken
    
    if ($lists -and $lists.value) {
        Write-Host "Found $($lists.value.Count) lists in the site" -ForegroundColor Cyan
        
        # Look for User Information List or similar
        $userInfoList = $lists.value | Where-Object { $_.displayName -like "*User*" -or $_.name -like "*User*" }
        if ($userInfoList) {
            Write-Host "Found User Information List: $($userInfoList.displayName)" -ForegroundColor Cyan
        }
    }
    
    # Step 3: Get Site Permissions
    Write-Host "`n--- Step 3: Getting Site Permissions ---" -ForegroundColor Yellow
    
    $permissionsUrl = "https://graph.microsoft.com/v1.0/sites/$siteId/permissions"
    $permissions = Invoke-GraphRequest -Uri $permissionsUrl -AccessToken $graphToken
    
    if ($permissions -and $permissions.value) {
        Write-Host "Found $($permissions.value.Count) permission entries" -ForegroundColor Cyan
        
        foreach ($permission in $permissions.value) {
            Write-Host "Permission ID: $($permission.id)" -ForegroundColor White
            Write-Host "  Roles: $($permission.roles -join ', ')" -ForegroundColor White
            
            if ($permission.grantedToIdentities) {
                foreach ($identity in $permission.grantedToIdentities) {
                    if ($identity.user) {
                        $owner = [PSCustomObject]@{
                            Name = $identity.user.displayName
                            Email = $identity.user.email
                            Id = $identity.user.id
                            Type = "User"
                            Source = "Site Permissions (Graph API)"
                            Roles = $permission.roles -join ', '
                        }
                        $allOwners += $owner
                        Write-Host "    User: $($identity.user.displayName) ($($identity.user.email)) - Roles: $($permission.roles -join ', ')" -ForegroundColor White
                    }
                    elseif ($identity.group) {
                        Write-Host "    Group: $($identity.group.displayName) - Roles: $($permission.roles -join ', ')" -ForegroundColor White
                        
                        # Get group members
                        $groupMembersUrl = "https://graph.microsoft.com/v1.0/groups/$($identity.group.id)/members"
                        $groupMembers = Invoke-GraphRequest -Uri $groupMembersUrl -AccessToken $graphToken

                        if ($groupMembers -and $groupMembers.value) {
                            Write-Host "    Group Members:" -ForegroundColor White
                            foreach ($member in $groupMembers.value) {
                                $memberObj = [PSCustomObject]@{
                                    Name = $member.displayName
                                    Email = $member.mail
                                    Id = $member.id
                                    Type = "User in Group"
                                    Source = "Site Permissions (Group: $($identity.group.displayName))"
                                    Roles = "Group Member"
                                }
                                $allOwners += $memberObj
                                Write-Host "      - $($member.displayName) ($($member.mail))" -ForegroundColor White
                            }
                        }
                    }
                }
            }
            elseif ($permission.grantedTo) {
                if ($permission.grantedTo.user) {
                    $owner = [PSCustomObject]@{
                        Name = $permission.grantedTo.user.displayName
                        Email = $permission.grantedTo.user.email
                        Id = $permission.grantedTo.user.id
                        Type = "User"
                        Source = "Site Permissions (Graph API)"
                        Roles = $permission.roles -join ', '
                    }
                    $allOwners += $owner
                    Write-Host "    User: $($permission.grantedTo.user.displayName) ($($permission.grantedTo.user.email)) - Roles: $($permission.roles -join ', ')" -ForegroundColor White
                }
            }
        }
    } else {
        Write-Host "No permissions found via Graph API" -ForegroundColor Yellow
    }
    
    # Step 4: Get Drive Information and Permissions
    Write-Host "`n--- Step 4: Getting Drive Information ---" -ForegroundColor Yellow
    
    $drivesUrl = "https://graph.microsoft.com/v1.0/sites/$siteId/drives"
    $drives = Invoke-GraphRequest -Uri $drivesUrl -AccessToken $graphToken
    
    if ($drives -and $drives.value) {
        Write-Host "Found $($drives.value.Count) drives" -ForegroundColor Cyan
        
        foreach ($drive in $drives.value) {
            Write-Host "Drive: $($drive.name) (Type: $($drive.driveType))" -ForegroundColor Cyan
            
            # Get drive permissions
            $drivePermUrl = "https://graph.microsoft.com/v1.0/drives/$($drive.id)/root/permissions"
            $drivePerms = Invoke-GraphRequest -Uri $drivePermUrl -AccessToken $graphToken
            
            if ($drivePerms -and $drivePerms.value) {
                foreach ($perm in $drivePerms.value) {
                    if ($perm.roles -contains "owner" -or $perm.roles -contains "write") {
                        if ($perm.grantedToIdentities) {
                            foreach ($identity in $perm.grantedToIdentities) {
                                if ($identity.user -and -not ($allOwners | Where-Object { $_.Email -eq $identity.user.email })) {
                                    $owner = [PSCustomObject]@{
                                        Name = $identity.user.displayName
                                        Email = $identity.user.email
                                        Id = $identity.user.id
                                        Type = "User"
                                        Source = "Drive: $($drive.name)"
                                        Roles = $perm.roles -join ', '
                                    }
                                    $allOwners += $owner
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    # Step 5: Export Results
    Write-Host "`n--- Step 7: Exporting Results ---" -ForegroundColor Yellow
    
    if ($allOwners.Count -gt 0) {
        # Remove duplicates
        $uniqueOwners = $allOwners | Sort-Object Email -Unique
        
        # Add timestamp
        foreach ($owner in $uniqueOwners) {
            $owner | Add-Member -NotePropertyName "Timestamp" -NotePropertyValue (Get-Date) -Force
            $owner | Add-Member -NotePropertyName "SiteURL" -NotePropertyValue $SiteURL -Force
        }
        
        $uniqueOwners | Export-Csv -Path "SiteOwners_GraphAPI_Hybrid.csv" -NoTypeInformation
        Write-Host "Results exported to SiteOwners_GraphAPI_Hybrid.csv" -ForegroundColor Green
        
        Write-Host "`n--- SUMMARY ---" -ForegroundColor Green
        Write-Host "Total Unique Site Owners Found via Graph API: $($uniqueOwners.Count)" -ForegroundColor White
        Write-Host "Site: $SiteURL" -ForegroundColor White
        Write-Host "Site ID: $siteId" -ForegroundColor White
        
        Write-Host "`nGraph API Owners List:" -ForegroundColor White
        foreach ($owner in $uniqueOwners) {
            Write-Host "  - $($owner.Name) ($($owner.Email)) - Source: $($owner.Source) - Roles: $($owner.Roles)" -ForegroundColor White
        }
        
    } else {
        Write-Host "No site owners found through Graph API" -ForegroundColor Red
    }
    
    Write-Host "`nScript completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Error "Script execution failed: $($_.Exception.Message)"
} finally {
    # Clean up connections

    Disconnect-PnPOnline -Connection $adminconnection
}