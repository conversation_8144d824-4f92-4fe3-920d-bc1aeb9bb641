#!/usr/bin/env python3
# PoC: reach site, authenticate with app-only creds, list doc libraries + top-level files/folders
# https://jabil.sharepoint.com/sites/LuminexData (ClientID should have access)
# https://jabil.sharepoint.com/sites/GlobalInformationSecurity-IdentityServices (ClientID should not have access)
# https://jabil.sharepoint.com/sites/HRIT_Integration_SAP_Functional (ClientID should not have access)
# RITM3357511
# Name: Luminex logging
# Client ID: de46023a-a9a8-4137-bc30-96de5f5c7de1
# Tenant ID: bc876b21-f134-4c12-a265-8ed26b7f0f3b
# Secret: ****************************************
# IT team is using pip install Office365-REST-Python-Client

import sys
from datetime import datetime
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.client_credential import ClientCredential

# --- config (from your message) ---
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"    # kept for clarity; the library auth uses client creds below
CLIENT_SECRET = "****************************************"
# ----------------------------------

def test_sharepoint_connection(site_url, client_id, client_secret):
    """Test SharePoint connection using proper authentication"""
    try:
        print(f"Testing connection to: {site_url}")
        print(f"Using Client ID: {client_id}")

        creds = ClientCredential(client_id, client_secret)
        ctx = ClientContext(site_url).with_credentials(creds)

        # Try to get basic site information
        web = ctx.web.get().execute_query()
        print(f"✓ Successfully connected to site: {web.properties.get('Title', 'Unknown')}")
        print(f"✓ Site URL: {web.properties.get('Url', 'Unknown')}")
        return ctx

    except Exception as e:
        print(f"✗ Connection failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        return None

def list_libraries_and_contents(ctx):
    """List document libraries and their contents"""
    try:
        # get all lists, filter document libraries (baseTemplate 101)
        lists = ctx.web.lists.get().execute_query()
        print(f"\nFound {len(lists)} lists total")

        doc_libraries = []
        for lst in lists:
            props = lst.properties
            if props.get("BaseTemplate") != 101:
                continue
            doc_libraries.append(lst)
            title = props.get("Title")
            print(f"\nLibrary: {title}")

            # get root folder for the list and expand files + folders
            root = lst.rootFolder.get().execute_query()
            files = root.files.get().execute_query()
            folders = root.folders.get().execute_query()

            if not files and not folders:
                print("  (empty)")
                continue

            if folders:
                print("  Folders:")
                for f in folders:
                    print("   -", f.properties.get("Name"), "|", f.properties.get("ServerRelativeUrl"))
            if files:
                print("  Files:")
                for f in files:
                    print("   -", f.properties.get("Name"), "|", f.properties.get("ServerRelativeUrl"))

        return doc_libraries

    except Exception as e:
        print(f"Error listing libraries: {str(e)}")
        return []

def manage_ruben_test_folder(ctx):
    """Create RubenTest folder, clear existing files, and add new timestamped file"""
    try:
        # Get the default document library (usually "Shared Documents")
        default_library = ctx.web.default_document_library()
        root_folder = default_library.root_folder

        folder_name = "RubenTest"
        folder_path = f"Shared Documents/{folder_name}"

        print(f"\n3) Managing {folder_name} folder...")

        # Try to get existing folder or create new one
        try:
            target_folder = ctx.web.get_folder_by_server_relative_url(f"/sites/LuminexData/{folder_path}")
            target_folder.get().execute_query()
            print(f"✓ Found existing folder: {folder_name}")

            # Delete all existing files in the folder
            files = target_folder.files.get().execute_query()
            if files:
                print(f"  Deleting {len(files)} existing files...")
                for file in files:
                    file.delete_object()
                    print(f"  - Deleted: {file.properties.get('Name')}")
                ctx.execute_query()
                print("  ✓ All existing files deleted")
            else:
                print("  No existing files to delete")

        except Exception:
            print(f"  Folder doesn't exist, creating new one...")
            target_folder = root_folder.folders.add(folder_name)
            ctx.execute_query()
            print(f"  ✓ Created new folder: {folder_name}")

        # Create new file with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"test_file_{timestamp}.txt"
        file_content = f"Test file created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\nThis file was created by the RubenPOC script."

        print(f"  Creating new file: {file_name}")
        target_folder.upload_file(file_name, file_content.encode('utf-8'))
        ctx.execute_query()
        print(f"  ✓ Successfully created file: {file_name}")

        return True

    except Exception as e:
        print(f"✗ Error managing RubenTest folder: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    print("=== SharePoint POC Script ===")
    print("1) Testing SharePoint connection...")

    # Test connection and get context
    ctx = test_sharepoint_connection(SITE, CLIENT_ID, CLIENT_SECRET)
    if not ctx:
        print("Cannot connect to SharePoint site. Please check:")
        print("- Client ID and Secret are correct")
        print("- App has proper permissions (Sites.Selected)")
        print("- Site URL is accessible")
        sys.exit(1)

    print("\n2) Listing document libraries and top-level contents...")
    try:
        libraries = list_libraries_and_contents(ctx)
        if not libraries:
            print("No document libraries found or accessible")
    except Exception as e:
        print(f"Error listing libraries: {str(e)}")
        print("This might indicate permission issues")

    # Manage RubenTest folder
    try:
        success = manage_ruben_test_folder(ctx)
        if success:
            print("\n✓ RubenTest folder management completed successfully!")
        else:
            print("\n✗ RubenTest folder management failed")
    except Exception as e:
        print(f"\nError managing RubenTest folder: {str(e)}")
        print("This might indicate insufficient permissions for file operations")

    print("\n=== Script completed ===")