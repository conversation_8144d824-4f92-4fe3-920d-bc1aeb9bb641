# PowerShell script to grant SharePoint site permissions to Azure AD app
# This script must be run by a SharePoint administrator

# App and site details
$AppId = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
$SiteUrl = "https://jabil.sharepoint.com/sites/LuminexData"
$AppDisplayName = "RubenPOC-App"  # Change this to your app's actual name
$Permission = "Write"  # Options: Read, Write, Manage, FullControl

Write-Host "SharePoint Site Permission Grant Script" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""
Write-Host "App ID: $AppId" -ForegroundColor Yellow
Write-Host "Site URL: $SiteUrl" -ForegroundColor Yellow
Write-Host "Permission Level: $Permission" -ForegroundColor Yellow
Write-Host ""

# Check if PnP PowerShell module is installed
Write-Host "Checking for PnP PowerShell module..." -ForegroundColor Cyan
$pnpModule = Get-Module -ListAvailable -Name "PnP.PowerShell"

if (-not $pnpModule) {
    Write-Host "PnP PowerShell module not found. Installing..." -ForegroundColor Yellow
    try {
        Install-Module -Name "PnP.PowerShell" -Force -AllowClobber -Scope CurrentUser
        Write-Host "✓ PnP PowerShell module installed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to install PnP PowerShell module: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please install manually: Install-Module -Name PnP.PowerShell" -ForegroundColor Yellow
        exit 1
    }
}
else {
    Write-Host "✓ PnP PowerShell module found" -ForegroundColor Green
}

# Connect to SharePoint Online
Write-Host ""
Write-Host "Connecting to SharePoint Online..." -ForegroundColor Cyan
Write-Host "You will be prompted to sign in with SharePoint admin credentials." -ForegroundColor Yellow

try {
    Connect-PnPOnline -Url $SiteUrl -Interactive
    Write-Host "✓ Connected to SharePoint Online" -ForegroundColor Green
}
catch {
    Write-Host "✗ Failed to connect to SharePoint Online: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure you have SharePoint admin permissions." -ForegroundColor Yellow
    exit 1
}

# Grant app permissions to the site
Write-Host ""
Write-Host "Granting app permissions to the site..." -ForegroundColor Cyan

try {
    Grant-PnPAzureADAppSitePermission -AppId $AppId -DisplayName $AppDisplayName -Site $SiteUrl -Permissions $Permission
    Write-Host "✓ Successfully granted $Permission permissions to app $AppDisplayName" -ForegroundColor Green
    Write-Host "  App ID: $AppId" -ForegroundColor Gray
    Write-Host "  Site: $SiteUrl" -ForegroundColor Gray
}
catch {
    Write-Host "✗ Failed to grant permissions: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "- App ID not found (check Azure AD app registration)" -ForegroundColor Yellow
    Write-Host "- Insufficient permissions (need SharePoint admin role)" -ForegroundColor Yellow
    Write-Host "- Site URL incorrect or inaccessible" -ForegroundColor Yellow
    exit 1
}

# Verify the permission grant
Write-Host ""
Write-Host "Verifying permission grant..." -ForegroundColor Cyan

try {
    $appPermissions = Get-PnPAzureADAppSitePermission
    $ourApp = $appPermissions | Where-Object { $_.AppId -eq $AppId }
    
    if ($ourApp) {
        Write-Host "✓ Permission grant verified!" -ForegroundColor Green
        Write-Host "  App Display Name: $($ourApp.AppDisplayName)" -ForegroundColor Gray
        Write-Host "  Permission Level: $($ourApp.Right)" -ForegroundColor Gray
        Write-Host "  Site: $($ourApp.SiteUrl)" -ForegroundColor Gray
    }
    else {
        Write-Host "⚠ Permission grant not found in verification, but this might be normal." -ForegroundColor Yellow
        Write-Host "  Try running your Python script to test if it works now." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "⚠ Could not verify permission grant: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "  This doesn't necessarily mean the grant failed." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Green
Write-Host "1. Run your Python script again: python RubenPOC.py" -ForegroundColor White
Write-Host "2. If it still fails, wait a few minutes for permissions to propagate" -ForegroundColor White
Write-Host "3. Check Azure AD app registration has Sites.Selected permission with admin consent" -ForegroundColor White
Write-Host ""
Write-Host "Script completed!" -ForegroundColor Green

# Disconnect
Disconnect-PnPOnline
