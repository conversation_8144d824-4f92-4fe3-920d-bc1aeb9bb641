#!/usr/bin/env python3
"""
SharePoint POC using Microsoft Graph API
This version uses Graph API which works with the current app permissions
"""

import sys
import requests
import json
from datetime import datetime
import io

# App credentials
SITE = "https://jabil.sharepoint.com/sites/LuminexData"
CLIENT_ID = "de46023a-a9a8-4137-bc30-96de5f5c7de1"
TENANT_ID = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
CLIENT_SECRET = "****************************************"

class GraphAPIClient:
    def __init__(self, client_id, client_secret, tenant_id):
        self.client_id = client_id
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        self.access_token = None
        self.site_id = None
        self.drive_id = None
    
    def get_access_token(self):
        """Get access token for Microsoft Graph"""
        token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        
        response = requests.post(token_url, headers=headers, data=data)
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data['access_token']
            return True
        else:
            print(f"Token request failed: {response.status_code}")
            print(response.text)
            return False
    
    def get_headers(self):
        """Get headers with authorization"""
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    
    def get_site_info(self, site_url):
        """Get site information and ID"""
        # Convert SharePoint URL to Graph API format
        # https://jabil.sharepoint.com/sites/LuminexData -> jabil.sharepoint.com:/sites/LuminexData
        site_path = site_url.replace('https://', '').replace('/sites/', ':/sites/')

        url = f"https://graph.microsoft.com/v1.0/sites/{site_path}"
        response = requests.get(url, headers=self.get_headers())

        if response.status_code == 200:
            site_data = response.json()
            self.site_id = site_data['id']
            return site_data
        else:
            print(f"Failed to get site info: {response.status_code}")
            print(f"Tried URL: {url}")
            print(response.text)
            return None
    
    def get_drives(self):
        """Get document libraries (drives) for the site"""
        if not self.site_id:
            return None
        
        url = f"https://graph.microsoft.com/v1.0/sites/{self.site_id}/drives"
        response = requests.get(url, headers=self.get_headers())
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Failed to get drives: {response.status_code}")
            print(response.text)
            return None
    
    def get_drive_items(self, drive_id, folder_path=""):
        """Get items in a drive/folder"""
        if folder_path:
            url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{folder_path}:/children"
        else:
            url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
        
        response = requests.get(url, headers=self.get_headers())
        
        if response.status_code == 200:
            return response.json()
        else:
            return None
    
    def create_folder(self, drive_id, folder_name, parent_path=""):
        """Create a folder in the drive"""
        if parent_path:
            url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{parent_path}:/children"
        else:
            url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root/children"
        
        data = {
            "name": folder_name,
            "folder": {},
            "@microsoft.graph.conflictBehavior": "replace"
        }
        
        response = requests.post(url, headers=self.get_headers(), json=data)
        
        if response.status_code == 201:
            return response.json()
        else:
            print(f"Failed to create folder: {response.status_code}")
            print(response.text)
            return None
    
    def delete_file(self, drive_id, file_path):
        """Delete a file"""
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{file_path}"
        response = requests.delete(url, headers=self.get_headers())
        
        return response.status_code == 204
    
    def upload_file(self, drive_id, file_path, file_content):
        """Upload a file"""
        url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/root:/{file_path}:/content"
        
        headers = self.get_headers()
        headers['Content-Type'] = 'text/plain'
        
        response = requests.put(url, headers=headers, data=file_content)
        
        if response.status_code in [200, 201]:
            return response.json()
        else:
            print(f"Failed to upload file: {response.status_code}")
            print(response.text)
            return None

def main():
    print("=== SharePoint POC using Microsoft Graph API ===")
    
    # Initialize Graph API client
    client = GraphAPIClient(CLIENT_ID, CLIENT_SECRET, TENANT_ID)
    
    # Get access token
    print("1) Getting access token...")
    if not client.get_access_token():
        print("Failed to get access token")
        sys.exit(1)
    print("✓ Access token obtained")
    
    # Get site information
    print("\n2) Getting site information...")
    site_info = client.get_site_info(SITE)
    if not site_info:
        print("Failed to get site information")
        sys.exit(1)
    
    print(f"✓ Connected to site: {site_info['displayName']}")
    print(f"  Site ID: {site_info['id']}")
    
    # Get document libraries
    print("\n3) Getting document libraries...")
    drives_data = client.get_drives()
    if not drives_data:
        print("Failed to get document libraries")
        sys.exit(1)
    
    drives = drives_data['value']
    print(f"✓ Found {len(drives)} document libraries:")
    
    main_drive = None
    for drive in drives:
        print(f"  - {drive['name']} (ID: {drive['id']})")
        if drive['name'] == 'Documents':  # Usually the main document library
            main_drive = drive
    
    if not main_drive:
        main_drive = drives[0]  # Use first available drive
    
    print(f"\nUsing drive: {main_drive['name']}")
    
    # Manage RubenTest folder
    print("\n4) Managing RubenTest folder...")
    folder_name = "RubenTest"
    
    # Check if folder exists
    folder_items = client.get_drive_items(main_drive['id'], folder_name)
    
    if folder_items:
        print(f"✓ Found existing folder: {folder_name}")
        # Delete existing files
        files_to_delete = [item for item in folder_items['value'] if 'file' in item]
        if files_to_delete:
            print(f"  Deleting {len(files_to_delete)} existing files...")
            for file_item in files_to_delete:
                file_path = f"{folder_name}/{file_item['name']}"
                if client.delete_file(main_drive['id'], file_path):
                    print(f"  - Deleted: {file_item['name']}")
                else:
                    print(f"  - Failed to delete: {file_item['name']}")
        else:
            print("  No existing files to delete")
    else:
        print(f"  Creating new folder: {folder_name}")
        folder_result = client.create_folder(main_drive['id'], folder_name)
        if folder_result:
            print(f"✓ Created folder: {folder_name}")
        else:
            print(f"✗ Failed to create folder: {folder_name}")
            sys.exit(1)
    
    # Create new timestamped file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"test_file_{timestamp}.txt"
    file_content = f"Test file created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\nThis file was created by the RubenPOC Graph API script.\nTimestamp: {timestamp}"
    
    print(f"\n5) Creating new file: {file_name}")
    file_path = f"{folder_name}/{file_name}"
    
    upload_result = client.upload_file(main_drive['id'], file_path, file_content)
    if upload_result:
        print(f"✓ Successfully created file: {file_name}")
        print(f"  File size: {upload_result.get('size', 'Unknown')} bytes")
        print(f"  Web URL: {upload_result.get('webUrl', 'Not available')}")
    else:
        print(f"✗ Failed to create file: {file_name}")
    
    print("\n=== POC Completed Successfully! ===")

if __name__ == "__main__":
    main()
